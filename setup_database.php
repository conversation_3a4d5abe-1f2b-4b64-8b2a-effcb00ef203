<?php
// Database setup script
include_once '.env.php';

echo "<h2>Database Setup Script</h2>";

// First, try to connect with root to create database and user
echo "<h3>Step 1: Creating Database and User</h3>";

$root_connection = mysqli_connect($_ENV['DB_HOST'], 'root', '');

if ($root_connection) {
    echo "<p style='color: green;'>✓ Connected to MySQL as root</p>";
    
    // Create database
    $create_db_sql = "CREATE DATABASE IF NOT EXISTS `" . $_ENV['DB_NAME'] . "`";
    if (mysqli_query($root_connection, $create_db_sql)) {
        echo "<p style='color: green;'>✓ Database '" . $_ENV['DB_NAME'] . "' created or already exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Error creating database: " . mysqli_error($root_connection) . "</p>";
    }
    
    // Create user and grant privileges
    $create_user_sql = "CREATE USER IF NOT EXISTS '" . $_ENV['DB_USERNAME'] . "'@'localhost' IDENTIFIED BY '" . $_ENV['DB_PASSWORD'] . "'";
    if (mysqli_query($root_connection, $create_user_sql)) {
        echo "<p style='color: green;'>✓ User '" . $_ENV['DB_USERNAME'] . "' created or already exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Error creating user: " . mysqli_error($root_connection) . "</p>";
    }
    
    // Grant privileges
    $grant_sql = "GRANT ALL PRIVILEGES ON `" . $_ENV['DB_NAME'] . "`.* TO '" . $_ENV['DB_USERNAME'] . "'@'localhost'";
    if (mysqli_query($root_connection, $grant_sql)) {
        echo "<p style='color: green;'>✓ Privileges granted to user '" . $_ENV['DB_USERNAME'] . "'</p>";
    } else {
        echo "<p style='color: red;'>✗ Error granting privileges: " . mysqli_error($root_connection) . "</p>";
    }
    
    // Flush privileges
    if (mysqli_query($root_connection, "FLUSH PRIVILEGES")) {
        echo "<p style='color: green;'>✓ Privileges flushed</p>";
    }
    
    mysqli_close($root_connection);
} else {
    echo "<p style='color: red;'>✗ Cannot connect to MySQL as root: " . mysqli_connect_error() . "</p>";
    echo "<p>Please make sure XAMPP MySQL is running and root user has no password set.</p>";
}

echo "<hr>";

// Now test connection with the new user
echo "<h3>Step 2: Testing Connection with New User</h3>";

$user_connection = mysqli_connect($_ENV['DB_HOST'], $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD'], $_ENV['DB_NAME']);

if ($user_connection) {
    echo "<p style='color: green;'>✓ Successfully connected with user '" . $_ENV['DB_USERNAME'] . "'</p>";
    
    // Test creating a simple table
    $test_table_sql = "CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if (mysqli_query($user_connection, $test_table_sql)) {
        echo "<p style='color: green;'>✓ Test table created successfully</p>";
        
        // Insert test data
        $insert_sql = "INSERT INTO test_table (name) VALUES ('Test Connection')";
        if (mysqli_query($user_connection, $insert_sql)) {
            echo "<p style='color: green;'>✓ Test data inserted successfully</p>";
            
            // Read test data
            $select_sql = "SELECT * FROM test_table ORDER BY id DESC LIMIT 1";
            $result = mysqli_query($user_connection, $select_sql);
            if ($result && mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                echo "<p style='color: green;'>✓ Test data retrieved: ID=" . $row['id'] . ", Name=" . $row['name'] . "</p>";
            }
        }
        
        // Clean up test table
        mysqli_query($user_connection, "DROP TABLE test_table");
        echo "<p style='color: blue;'>ℹ Test table cleaned up</p>";
    } else {
        echo "<p style='color: red;'>✗ Error creating test table: " . mysqli_error($user_connection) . "</p>";
    }
    
    mysqli_close($user_connection);
} else {
    echo "<p style='color: red;'>✗ Cannot connect with user '" . $_ENV['DB_USERNAME'] . "': " . mysqli_connect_error() . "</p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>If all tests passed, your database is ready!</li>";
echo "<li>You can now access your application at: <a href='http://localhost/m-hellotrade-live-soft/' target='_blank'>http://localhost/m-hellotrade-live-soft/</a></li>";
echo "<li>If you need to import database tables, you can use phpMyAdmin at: <a href='http://localhost/phpmyadmin/' target='_blank'>http://localhost/phpmyadmin/</a></li>";
echo "<li>Login to phpMyAdmin with username: <strong>" . $_ENV['DB_USERNAME'] . "</strong> and the password you set</li>";
echo "</ol>";

echo "<p><em>Setup completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
