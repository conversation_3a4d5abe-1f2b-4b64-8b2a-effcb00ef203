<?php
include_once '.env.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Import Guide</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .code { background: #f0f0f0; padding: 10px; font-family: monospace; margin: 10px 0; }
        .highlight { background: yellow; padding: 2px; }
    </style>
</head>
<body>
    <h1>🗄️ Database Import Guide</h1>
    
    <p>Your local database is now set up, but it's empty. Here's how to import your data from the remote database:</p>
    
    <div class="step">
        <h3>Step 1: Export from Remote Database</h3>
        <ol>
            <li>Go to your remote database panel: <a href="https://dev.hellotrade.live/soft/dbpanel.php?username=hellotrade_user&db=hellotrade_db" target="_blank">Remote DB Panel</a></li>
            <li>Login with:
                <ul>
                    <li><strong>Username:</strong> <?php echo $_ENV['DB_USERNAME']; ?></li>
                    <li><strong>Password:</strong> <?php echo $_ENV['DB_PASSWORD']; ?></li>
                </ul>
            </li>
            <li>Look for an "Export" or "Backup" option</li>
            <li>Export the entire database as SQL file</li>
            <li>Download the SQL file to your computer</li>
        </ol>
    </div>
    
    <div class="step">
        <h3>Step 2: Import to Local Database</h3>
        <p><strong>Option A: Using phpMyAdmin (Recommended)</strong></p>
        <ol>
            <li>Open phpMyAdmin: <a href="http://localhost/phpmyadmin/" target="_blank">http://localhost/phpmyadmin/</a></li>
            <li>Login with:
                <ul>
                    <li><strong>Username:</strong> <?php echo $_ENV['DB_USERNAME']; ?></li>
                    <li><strong>Password:</strong> <?php echo $_ENV['DB_PASSWORD']; ?></li>
                </ul>
            </li>
            <li>Select the database: <strong><?php echo $_ENV['DB_NAME']; ?></strong></li>
            <li>Click on "Import" tab</li>
            <li>Choose the SQL file you downloaded</li>
            <li>Click "Go" to import</li>
        </ol>
        
        <p><strong>Option B: Using Command Line</strong></p>
        <div class="code">
mysql -u <?php echo $_ENV['DB_USERNAME']; ?> -p <?php echo $_ENV['DB_NAME']; ?> < path/to/your/exported_file.sql
        </div>
    </div>
    
    <div class="step">
        <h3>Step 3: Verify Import</h3>
        <ol>
            <li>Test your connection: <a href="test_connection.php" target="_blank">Test Connection</a></li>
            <li>Check if tables are imported correctly</li>
            <li>Test your application: <a href="index.php" target="_blank">Main Application</a></li>
        </ol>
    </div>
    
    <div class="step">
        <h3>Alternative: Create Basic Tables</h3>
        <p>If you can't export from the remote database, I can help you create the basic table structure. Common tables for this type of application usually include:</p>
        <ul>
            <li><code>user</code> - User accounts</li>
            <li><code>settings</code> - Site settings</li>
            <li><code>deposit_block</code> - Deposit transactions</li>
            <li><code>message</code> - Internal messaging</li>
            <li>And others...</li>
        </ul>
        <p>Let me know if you need help creating these tables manually.</p>
    </div>
    
    <hr>
    
    <h3>🔗 Quick Links</h3>
    <ul>
        <li><a href="test_connection.php">Test Database Connection</a></li>
        <li><a href="http://localhost/phpmyadmin/" target="_blank">phpMyAdmin</a></li>
        <li><a href="https://dev.hellotrade.live/soft/dbpanel.php" target="_blank">Remote Database Panel</a></li>
        <li><a href="index.php">Your Application</a></li>
    </ul>
    
    <h3>📋 Current Configuration</h3>
    <div class="code">
Host: <?php echo $_ENV['DB_HOST']; ?><br>
Username: <?php echo $_ENV['DB_USERNAME']; ?><br>
Database: <?php echo $_ENV['DB_NAME']; ?><br>
Password: Set (hidden)
    </div>
    
    <p><em>Guide created at: <?php echo date('Y-m-d H:i:s'); ?></em></p>
</body>
</html>
