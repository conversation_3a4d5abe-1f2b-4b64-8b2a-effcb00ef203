{"version": 3, "file": "../tiny-slider.css", "sources": ["tiny-slider.scss"], "sourcesContent": ["// Version: 2.9.3\n\n.tns-outer {\n  padding: 0 !important; // remove padding: clientWidth = width + padding (0) = width\n  [hidden] { display: none !important; }\n  [aria-controls], [data-action] { cursor: pointer; }\n}\n.tns-slider {\n  -webkit-transition: all 0s;\n  -moz-transition: all 0s;\n  transition: all 0s;\n  > .tns-item {\n    -webkit-box-sizing: border-box;\n    -moz-box-sizing: border-box;\n    box-sizing: border-box;\n  }\n}\n\n.tns-horizontal {\n  &.tns-subpixel {\n    white-space: nowrap;\n    > .tns-item {\n      display: inline-block;\n      vertical-align: top;\n      white-space: normal;\n    }\n  }\n  &.tns-no-subpixel {\n    &:after {\n      content: '';\n      display: table;\n      clear: both;\n    }\n    > .tns-item {\n      float: left;\n    }\n  }\n  &.tns-carousel {\n    &.tns-no-subpixel {\n      > .tns-item {\n        margin-right: -100%;\n      }\n    }\n  }\n}\n.tns-no-calc {\n  position: relative;\n  left: 0;\n}\n.tns-gallery {\n  position: relative;\n  left: 0;\n  min-height: 1px; // make sure slider container is visible\n  // overflow: hidden;\n  > .tns-item {\n    position: absolute;\n    left: -100%;\n    -webkit-transition: transform 0s, opacity 0s;\n    -moz-transition: transform 0s, opacity 0s;\n    transition: transform 0s, opacity 0s;\n  }\n  > .tns-slide-active {\n    position: relative;\n    left: auto !important;\n  }\n  > .tns-moving {\n    -webkit-transition: all 0.25s;\n    -moz-transition: all 0.25s;\n    transition: all 0.25s;\n  }\n}\n.tns-autowidth { display: inline-block; }\n.tns-lazy-img {\n  -webkit-transition: opacity 0.6s;\n  -moz-transition: opacity 0.6s;\n  transition: opacity 0.6s;\n  opacity: 0.6;\n  &.tns-complete { opacity: 1; }\n}\n.tns-ah {\n  -webkit-transition: height 0s;\n  -moz-transition: height 0s;\n  transition: height 0s;\n}\n.tns-ovh { overflow: hidden; }\n.tns-visually-hidden { position: absolute; left: -10000em; }\n.tns-transparent { opacity: 0; visibility: hidden; }\n\n.tns-fadeIn {\n  opacity: 1;\n  filter: alpha(opacity=100);\n  z-index: 0;\n}\n.tns-normal, .tns-fadeOut {\n  opacity: 0;\n  filter: alpha(opacity=0);\n  z-index: -1;\n}\n\n\n// *** Fix a viewport issue in initialization\n.tns-vpfix {\n  white-space: nowrap;\n  > div, > li {\n    display: inline-block;\n  }\n}\n\n// *** Detecting browser capability ***\n$width: 310px;\n$height: 10px;\n$count: 70;\n$perpage: 3;\n\n.tns-t {\n  &-subp2 {\n    margin: 0 auto;\n    width: $width;\n    position: relative;\n    height: $height;\n    overflow: hidden;\n  }\n  &-ct {\n    width: (100% * $count / $perpage);\n    width: -webkit-calc(100% * #{$count} / #{$perpage});\n    width: -moz-calc(100% * #{$count} / #{$perpage});\n    width: calc(100% * #{$count} / #{$perpage});\n    position: absolute;\n    right: 0;\n    &:after {\n      content: '';\n      display: table;\n      clear: both;\n    }\n    > div {\n      width: (100% / $count);\n      width: -webkit-calc(100% / #{$count});\n      width: -moz-calc(100% / #{$count});\n      width: calc(100% / #{$count});\n      height: $height;\n      float: left;\n    }\n  }\n}\n"], "names": [], "mappings": "AAEA,AAAA,UAAU,AAAC,CACT,OAAO,CAAE,YAAY,CAGtB,AAJD,AAEE,UAFQ,EAER,AAAA,MAAC,AAAA,CAAQ,CAAE,OAAO,CAAE,eAAe,CAAI,AAFzC,AAGE,UAHQ,EAGR,AAAA,aAAC,AAAA,EAHH,UAAU,EAGS,AAAA,WAAC,AAAA,CAAa,CAAE,MAAM,CAAE,OAAO,CAAI,AAEtD,AAAA,WAAW,AAAC,CACV,kBAAkB,CAAE,MAAM,CAC1B,eAAe,CAAE,MAAM,CACvB,UAAU,CAAE,MAAM,CAMnB,AATD,AAIE,WAJS,CAIP,SAAS,AAAC,CACV,kBAAkB,CAAE,UAAU,CAC9B,eAAe,CAAE,UAAU,CAC3B,UAAU,CAAE,UAAU,CACvB,AAGH,AACE,eADa,AACZ,aAAa,AAAC,CACb,WAAW,CAAE,MAAM,CAMpB,AARH,AAGI,eAHW,AACZ,aAAa,CAEV,SAAS,AAAC,CACV,OAAO,CAAE,YAAY,CACrB,cAAc,CAAE,GAAG,CACnB,WAAW,CAAE,MAAM,CACpB,AAPL,AAUI,eAVW,AASZ,gBAAgB,AACd,MAAM,AAAC,CACN,OAAO,CAAE,EAAE,CACX,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,IAAI,CACZ,AAdL,AAeI,eAfW,AASZ,gBAAgB,CAMb,SAAS,AAAC,CACV,KAAK,CAAE,IAAI,CACZ,AAjBL,AAqBM,eArBS,AAmBZ,aAAa,AACX,gBAAgB,CACb,SAAS,AAAC,CACV,YAAY,CAAE,KAAK,CACpB,AAIP,AAAA,YAAY,AAAC,CACX,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,CAAC,CACR,AACD,AAAA,YAAY,AAAC,CACX,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,CAAC,CACP,UAAU,CAAE,GAAG,CAkBhB,AArBD,AAKE,YALU,CAKR,SAAS,AAAC,CACV,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,KAAK,CACX,kBAAkB,CAAE,wBAAwB,CAC5C,eAAe,CAAE,wBAAwB,CACzC,UAAU,CAAE,wBAAwB,CACrC,AAXH,AAYE,YAZU,CAYR,iBAAiB,AAAC,CAClB,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,eAAe,CACtB,AAfH,AAgBE,YAhBU,CAgBR,WAAW,AAAC,CACZ,kBAAkB,CAAE,SAAS,CAC7B,eAAe,CAAE,SAAS,CAC1B,UAAU,CAAE,SAAS,CACtB,AAEH,AAAA,cAAc,AAAC,CAAE,OAAO,CAAE,YAAY,CAAI,AAC1C,AAAA,aAAa,AAAC,CACZ,kBAAkB,CAAE,YAAY,CAChC,eAAe,CAAE,YAAY,CAC7B,UAAU,CAAE,YAAY,CACxB,OAAO,CAAE,GAAG,CAEb,AAND,AAKE,aALW,AAKV,aAAa,AAAC,CAAE,OAAO,CAAE,CAAC,CAAI,AAEjC,AAAA,OAAO,AAAC,CACN,kBAAkB,CAAE,SAAS,CAC7B,eAAe,CAAE,SAAS,CAC1B,UAAU,CAAE,SAAS,CACtB,AACD,AAAA,QAAQ,AAAC,CAAE,QAAQ,CAAE,MAAM,CAAI,AAC/B,AAAA,oBAAoB,AAAC,CAAE,QAAQ,CAAE,QAAQ,CAAE,IAAI,CAAE,QAAQ,CAAI,AAC7D,AAAA,gBAAgB,AAAC,CAAE,OAAO,CAAE,CAAC,CAAE,UAAU,CAAE,MAAM,CAAI,AAErD,AAAA,WAAW,AAAC,CACV,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,kBAAkB,CAC1B,OAAO,CAAE,CAAC,CACX,AACD,AAAA,WAAW,CAAE,YAAY,AAAC,CACxB,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,gBAAgB,CACxB,OAAO,CAAE,EAAE,CACZ,AAID,AAAA,UAAU,AAAC,CACT,WAAW,CAAE,MAAM,CAIpB,AALD,AAEE,UAFQ,CAEN,GAAG,CAFP,UAAU,CAEC,EAAE,AAAC,CACV,OAAO,CAAE,YAAY,CACtB,AAUA,AAAD,YAAO,AAAC,CACN,MAAM,CAAE,MAAM,CACd,KAAK,CARD,KAAK,CAST,QAAQ,CAAE,QAAQ,CAClB,MAAM,CATD,IAAI,CAUT,QAAQ,CAAE,MAAM,CACjB,AACA,AAAD,SAAI,AAAC,CACH,KAAK,CAAE,aAA0B,CACjC,KAAK,CAAE,2BAA4C,CACnD,KAAK,CAAE,wBAAyC,CAChD,KAAK,CAAE,mBAAoC,CAC3C,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,CAAC,CAcT,AApBA,AAOC,SAPE,AAOD,MAAM,AAAC,CACN,OAAO,CAAE,EAAE,CACX,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,IAAI,CACZ,AAXF,AAYC,SAZE,CAYA,GAAG,AAAC,CACJ,KAAK,CAAE,UAAe,CACtB,KAAK,CAAE,uBAA8B,CACrC,KAAK,CAAE,oBAA2B,CAClC,KAAK,CAAE,eAAsB,CAC7B,MAAM,CA7BH,IAAI,CA8BP,KAAK,CAAE,IAAI,CACZ"}