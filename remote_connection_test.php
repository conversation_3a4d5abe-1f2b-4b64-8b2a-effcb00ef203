<?php
// Advanced remote database connection test
include_once '.env.php';

echo "<h2>🌐 Remote Database Connection Test</h2>";
echo "<p>Testing connection to: <strong>" . $_ENV['DB_HOST'] . "</strong></p>";

$hosts_to_try = [
    'dev.hellotrade.live',
    'dev.hellotrade.live:3306',
    'mysql.dev.hellotrade.live',
    'db.dev.hellotrade.live',
    'hellotrade.live',
    'www.dev.hellotrade.live'
];

$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];
$database = $_ENV['DB_NAME'];

echo "<h3>🔍 Testing Different Host Configurations</h3>";

foreach ($hosts_to_try as $host) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
    echo "<h4>Testing: <code>$host</code></h4>";
    
    // Test connection
    $connection = @mysqli_connect($host, $username, $password, $database);
    
    if ($connection) {
        echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Connected to $host</p>";
        
        // Test a simple query
        $result = @mysqli_query($connection, "SHOW TABLES");
        if ($result) {
            $table_count = mysqli_num_rows($result);
            echo "<p style='color: green;'>✅ Query successful! Found $table_count tables.</p>";
            
            if ($table_count > 0) {
                echo "<p><strong>Tables found:</strong></p><ul>";
                $count = 0;
                while ($row = mysqli_fetch_array($result) && $count < 10) {
                    echo "<li>" . $row[0] . "</li>";
                    $count++;
                }
                if ($table_count > 10) {
                    echo "<li><em>... and " . ($table_count - 10) . " more tables</em></li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Connected but cannot query: " . mysqli_error($connection) . "</p>";
        }
        
        mysqli_close($connection);
        
        // If successful, update the .env.php file
        echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>";
        echo "<h4>🎉 Connection Successful!</h4>";
        echo "<p>This host works! You can update your .env.php file to use: <code>$host</code></p>";
        echo "</div>";
        break; // Stop testing once we find a working connection
        
    } else {
        $error = mysqli_connect_error();
        echo "<p style='color: red;'>❌ Failed: $error</p>";
    }
    
    echo "</div>";
}

echo "<hr>";

echo "<h3>🔧 Alternative Solutions</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; margin: 10px 0;'>";
echo "<h4>Option 1: Export/Import Database</h4>";
echo "<ol>";
echo "<li>Go to your remote database panel: <a href='https://dev.hellotrade.live/soft/dbpanel.php' target='_blank'>Remote DB Panel</a></li>";
echo "<li>Export the database as SQL file</li>";
echo "<li>Import it to your local XAMPP database</li>";
echo "<li>Use localhost for development</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff8f0; padding: 15px; margin: 10px 0;'>";
echo "<h4>Option 2: SSH Tunnel (Advanced)</h4>";
echo "<p>If you have SSH access to the server, you can create an SSH tunnel:</p>";
echo "<code>ssh -L 3307:localhost:3306 <EMAIL></code>";
echo "<p>Then connect to localhost:3307</p>";
echo "</div>";

echo "<div style='background: #f8f0ff; padding: 15px; margin: 10px 0;'>";
echo "<h4>Option 3: API Wrapper</h4>";
echo "<p>Create a simple API on the remote server that your local application can call via HTTP requests.</p>";
echo "</div>";

echo "<h3>📋 Current Configuration</h3>";
echo "<div style='background: #f5f5f5; padding: 10px; font-family: monospace;'>";
echo "Host: " . $_ENV['DB_HOST'] . "<br>";
echo "Username: " . $_ENV['DB_USERNAME'] . "<br>";
echo "Database: " . $_ENV['DB_NAME'] . "<br>";
echo "Password: " . (empty($_ENV['DB_PASSWORD']) ? 'Empty' : 'Set (hidden)') . "<br>";
echo "</div>";

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
