<?php
// SQL Import Script
include_once '.env.php';

// Switch back to localhost for import
$_ENV['DB_HOST'] = 'localhost';

echo "<h2>📥 SQL Import Tool</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['sql_file'])) {
    $uploadedFile = $_FILES['sql_file'];
    
    if ($uploadedFile['error'] == 0) {
        $sqlContent = file_get_contents($uploadedFile['tmp_name']);
        
        $connection = mysqli_connect($_ENV['DB_HOST'], $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD'], $_ENV['DB_NAME']);
        
        if (!$connection) {
            die("<p style='color: red;'>❌ Connection failed: " . mysqli_connect_error() . "</p>");
        }
        
        echo "<p style='color: green;'>✅ Connected to local database</p>";
        
        // Split SQL into individual queries
        $queries = explode(';', $sqlContent);
        $success_count = 0;
        $error_count = 0;
        
        echo "<h3>🔄 Executing SQL Queries...</h3>";
        echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;'>";
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                if (mysqli_query($connection, $query)) {
                    $success_count++;
                    echo "<p style='color: green; font-size: 12px;'>✅ Query executed successfully</p>";
                } else {
                    $error_count++;
                    echo "<p style='color: red; font-size: 12px;'>❌ Error: " . mysqli_error($connection) . "</p>";
                }
            }
        }
        
        echo "</div>";
        
        mysqli_close($connection);
        
        echo "<hr>";
        echo "<h3>📊 Import Summary</h3>";
        echo "<p><strong>Successful queries:</strong> $success_count</p>";
        echo "<p><strong>Failed queries:</strong> $error_count</p>";
        
        if ($error_count == 0) {
            echo "<p style='color: green; font-size: 18px;'>🎉 <strong>Import completed successfully!</strong></p>";
            echo "<p>Your application should now work with the remote database data.</p>";
            echo "<p><a href='index.php' target='_blank'>Test Your Application</a></p>";
        } else {
            echo "<p style='color: orange;'>⚠ Import completed with some errors. Check the log above.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ File upload error: " . $uploadedFile['error'] . "</p>";
    }
} else {
?>

<div style="max-width: 600px;">
    <h3>📋 Instructions</h3>
    <ol>
        <li><strong>Export from Remote Database:</strong>
            <ul>
                <li>Go to: <a href="https://dev.hellotrade.live/soft/dbpanel.php" target="_blank">Remote Database Panel</a></li>
                <li>Login with username: <code><?php echo $_ENV['DB_USERNAME']; ?></code></li>
                <li>Look for "Export" or "Backup" option</li>
                <li>Download the SQL file</li>
            </ul>
        </li>
        <li><strong>Upload and Import:</strong>
            <ul>
                <li>Use the form below to upload the SQL file</li>
                <li>Click "Import" to import the data</li>
            </ul>
        </li>
    </ol>

    <form method="post" enctype="multipart/form-data" style="background: #f9f9f9; padding: 20px; border-radius: 5px;">
        <h3>📤 Upload SQL File</h3>
        <p>
            <label for="sql_file"><strong>Select SQL file:</strong></label><br>
            <input type="file" name="sql_file" id="sql_file" accept=".sql" required style="margin: 10px 0;">
        </p>
        <p>
            <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer;">
                📥 Import Database
            </button>
        </p>
    </form>

    <div style="background: #e8f4fd; padding: 15px; margin: 20px 0; border-radius: 5px;">
        <h4>💡 Alternative: Use phpMyAdmin</h4>
        <p>You can also import the SQL file using phpMyAdmin:</p>
        <ol>
            <li>Go to <a href="http://localhost/phpmyadmin/" target="_blank">phpMyAdmin</a></li>
            <li>Login with username: <code><?php echo $_ENV['DB_USERNAME']; ?></code></li>
            <li>Select database: <code><?php echo $_ENV['DB_NAME']; ?></code></li>
            <li>Click "Import" tab</li>
            <li>Choose your SQL file and click "Go"</li>
        </ol>
    </div>
</div>

<?php
}

echo "<p><em>Import tool loaded at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
