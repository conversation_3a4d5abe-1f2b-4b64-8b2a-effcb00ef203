.bg-primary,
.bg-soft-primary,
.btn-primary,
.btn-soft-primary:hover, 
.btn-soft-primary:focus, 
.btn-soft-primary:active, 
.btn-soft-primary.active, 
.btn-soft-primary.focus,
.btn-outline-primary:hover, 
.btn-outline-primary:focus, 
.btn-outline-primary:active, 
.btn-outline-primary.active, 
.btn-outline-primary.focus, 
.btn-outline-primary:not(:disabled):not(.disabled):active,
.pagination .page-item.active .page-link,
.form-check-input.form-check-input:checked,
.nav-pills .nav-link.active,
#preloader #status .spinner .double-bounce1, 
#preloader #status .spinner .double-bounce2,
.social-icon li a:hover,
#topnav .navbar-toggle.open span:hover,
.feature.feature-primary .btn-color,
.feature.feature-primary .btn-soft:hover, 
.feature.feature-primary .btn-soft:focus, 
.feature.feature-primary .btn-soft:active, 
.feature.feature-primary .btn-soft.active, 
.feature.feature-primary .btn-soft.focus,
.tns-nav button.tns-nav-active,
.tiny-timeline:before,
.tiny-timeline .item-box:before, 
.tiny-timeline .item-box:after,
.tagcloud > a:hover,
.nft-items.nft-item-primary .bg-color,
.nft-creator.nft-creator-primary .btn {
    background-color: #0f7173 !important;
}

.btn-primary,
.btn-soft-primary:hover, 
.btn-soft-primary:focus, 
.btn-soft-primary:active, 
.btn-soft-primary.active, 
.btn-soft-primary.focus,
.btn-outline-primary,
.btn-outline-primary:hover, 
.btn-outline-primary:focus, 
.btn-outline-primary:active, 
.btn-outline-primary.active, 
.btn-outline-primary.focus, 
.btn-outline-primary:not(:disabled):not(.disabled):active,
.alert-primary,
.alert-outline-primary,
.pagination .page-item.active .page-link,
.form-control:focus,
.form-check-input:focus,
.form-check-input.form-check-input:checked,
.social-icon li a:hover,
#topnav .has-submenu.active.active .menu-arrow,
#topnav .has-submenu .submenu .has-submenu:hover .submenu-arrow,
#topnav.scroll .navigation-menu > li:hover > .menu-arrow, 
#topnav.scroll .navigation-menu > li.active > .menu-arrow,
#topnav.nav-sticky .navigation-menu.nav-light > li:hover > .menu-arrow, 
#topnav.nav-sticky .navigation-menu.nav-light > li.active > .menu-arrow,
#topnav .navigation-menu > li:hover > .menu-arrow,
.feature.feature-primary .btn-color,
.feature.feature-primary .btn-soft:hover, 
.feature.feature-primary .btn-soft:focus, 
.feature.feature-primary .btn-soft:active, 
.feature.feature-primary .btn-soft.active, 
.feature.feature-primary .btn-soft.focus,
.tns-nav button.tns-nav-active,
.nft-creator.nft-creator-primary .btn {
    border-color: #0f7173 !important;
}

.text-primary,
.btn-soft-primary,
.btn-outline-primary,
.alert-outline-primary,
.breadcrumb .breadcrumb-item a:hover,
.breadcrumb .breadcrumb-item.active,
.accordion .accordion-item .accordion-button:after,
.accordion .accordion-item .accordion-button:not(.collapsed),
.apps-links:hover .icon,
#topnav .has-submenu.active .submenu li.active > a,
#topnav .navigation-menu > li:hover > a,
#topnav .navigation-menu > li.active > a,
#topnav .navigation-menu > li > a:hover, 
#topnav .navigation-menu > li > a:active,
#topnav.scroll .navigation-menu > li:hover > a, 
#topnav.scroll .navigation-menu > li.active > a,
#topnav.nav-sticky .navigation-menu.nav-light > li.active > a,
#topnav.nav-sticky .navigation-menu.nav-light > li:hover > a, 
#topnav .navigation-menu > li .submenu li a:hover,
#topnav .navigation-menu > li:hover > a,
#topnav .navigation-menu > li.active > a,
#topnav .navigation-menu > li > a:hover,
#topnav .navigation-menu > li .submenu li a:hover,
#topnav .navigation-menu > li.has-submenu.open > a,
#topnav .has-submenu.active a,
#navmenu-nav li.active a,
#navmenu-nav li.account-menu.active .navbar-link, 
#navmenu-nav li.account-menu:hover .navbar-link,
.feature.feature-primary .icons i,
.feature.feature-primary .title:hover,
.feature.feature-primary .link,
.feature.feature-primary .btn-soft,
.key-feature.feature-primary .icon,
.blog.blog-primary .title:hover,
.blog.blog-primary .link,
.nft-items.nft-item-primary .content .title:hover,
.nft-items.nft-item-primary .content .author .name:hover,
.nft-creator.nft-creator-primary .content .author .name:hover,
.nft-creator.nft-creator-primary .bg-soft,
.nft-creator.nft-creator-primary .read-more:hover,
.nft-collection.nft-col-primary .content .author .name:hover,
.nft-collection.nft-col-primary .content .title:hover,
.nft-collection.nft-col-primary .content .bg-soft {
    color: #0f7173 !important;
}

a.text-primary:hover, a.text-primary:focus {
    color: #0c5b5c !important;
}

.bg-soft-primary,
.btn-soft-primary,
.feature.feature-primary .btn-soft,
.nft-creator.nft-creator-primary .bg-soft,
.nft-collection.nft-col-primary .content .bg-soft {
    background-color: rgba(15, 113, 115, 0.1) !important;
    border: 1px solid rgba(15, 113, 115, 0.1) !important;
}

.btn-primary,
.btn-outline-primary:hover, 
.btn-outline-primary:focus, 
.btn-outline-primary:active, 
.btn-outline-primary.active, 
.btn-outline-primary.focus, 
.btn-outline-primary:not(:disabled):not(.disabled):active,
.feature.feature-primary .btn-color,
.feature.feature-primary .btn-soft,
.nft-creator.nft-creator-primary .btn {
    box-shadow: 0 3px 5px 0 rgba(15, 113, 115, 0.3) !important;
}

.btn-primary:hover, 
.btn-primary:focus, 
.btn-primary:active, 
.btn-primary.active, 
.btn-primary.focus,
.feature.feature-primary .btn-color:hover, 
.feature.feature-primary .btn-color:focus, 
.feature.feature-primary .btn-color:active, 
.feature.feature-primary .btn-color.active, 
.feature.feature-primary .btn-color.focus,
.nft-creator.nft-creator-primary .btn:hover, 
.nft-creator.nft-creator-primary .btn:focus, 
.nft-creator.nft-creator-primary .btn:active, 
.nft-creator.nft-creator-primary .btn.active, 
.nft-creator.nft-creator-primary .btn.focus {
    background-color: #094546 !important;
    border-color: #094546 !important;
}

.alert-primary,
.pagination .page-item .page-link:hover {
    background-color: rgba(15, 113, 115, 0.9) !important;
}

.alert-primary .alert-link {
    color: #0000cc !important;
}

.pagination .page-item .page-link:hover {
    border-color: rgba(15, 113, 115, 0.9) !important;
}

::selection {
    background: rgba(15, 113, 115, 0.9) !important;
}

.bg-gradient-primary {
    background: linear-gradient(to left, #553880 0%, #0c5b5c 100%) !important;
}

.bg-gradient-primary-2 {
    background: linear-gradient(to bottom, #553880 0%, #0c5b5c 100%) !important;
}

.feature.feature-primary .icons i,
.key-feature.feature-primary .icon {
    background: linear-gradient(45deg, transparent, rgba(15, 113, 115, 0.2)) !important;
}

.tns-nav button {
    background: rgba(15, 113, 115, 0.5) !important;    
}