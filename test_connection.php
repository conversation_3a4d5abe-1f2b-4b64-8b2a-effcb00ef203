<?php
// Test database connection
include_once '.env.php';

echo "<h2>Database Connection Test</h2>";
echo "<p><strong>Host:</strong> " . $_ENV['DB_HOST'] . "</p>";
echo "<p><strong>Username:</strong> " . $_ENV['DB_USERNAME'] . "</p>";
echo "<p><strong>Database:</strong> " . $_ENV['DB_NAME'] . "</p>";
echo "<p><strong>Password:</strong> " . (empty($_ENV['DB_PASSWORD']) ? 'Empty' : 'Set (hidden)') . "</p>";

echo "<hr>";

// Test connection
$connection = mysqli_connect($_ENV['DB_HOST'], $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD'], $_ENV['DB_NAME']);

if ($connection) {
    echo "<p style='color: green;'><strong>✓ Database connection successful!</strong></p>";
    
    // Test if we can query the database
    $result = mysqli_query($connection, "SHOW TABLES");
    if ($result) {
        $table_count = mysqli_num_rows($result);
        echo "<p style='color: green;'>✓ Database query successful! Found {$table_count} tables.</p>";
        
        if ($table_count > 0) {
            echo "<h3>Tables in database:</h3><ul>";
            while ($row = mysqli_fetch_array($result)) {
                echo "<li>" . $row[0] . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Connected but cannot query database: " . mysqli_error($connection) . "</p>";
    }
    
    mysqli_close($connection);
} else {
    echo "<p style='color: red;'><strong>✗ Database connection failed!</strong></p>";
    echo "<p style='color: red;'>Error: " . mysqli_connect_error() . "</p>";
    
    echo "<h3>Troubleshooting steps:</h3>";
    echo "<ol>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check if the database 'hellotrade_db' exists</li>";
    echo "<li>Verify the username 'hellotrade_user' exists and has proper permissions</li>";
    echo "<li>Check if the password is correct</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<h3>XAMPP Status Check:</h3>";

// Check if we can connect to localhost MySQL at all
$test_connection = @mysqli_connect('localhost', 'root', '');
if ($test_connection) {
    echo "<p style='color: green;'>✓ XAMPP MySQL is running (can connect with root)</p>";
    mysqli_close($test_connection);
} else {
    echo "<p style='color: red;'>✗ XAMPP MySQL is not running or not accessible</p>";
}

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
