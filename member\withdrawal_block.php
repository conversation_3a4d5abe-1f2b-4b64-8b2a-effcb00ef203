<?php $type = (isset($_GET['type']) && in_array($_GET['type'], array(1,2,3,4,5,6,7,8,9,10, 11))) ? $_GET['type'] : 1;
$typearr = array(10=>'USDT', 11=>'USDT');
$type2 = $typearr[$type];
$title = "Withdrawal ".$type2;
$_is_dashboard = 1;
include_once 'header.php';
$wallet_field = ($type == 11) ? 'wallet_promo' : 'wallet';
?>
<style>
    body, #page-wrapper {
        background-color: #0b0e11;
    }
    .content-header {
        display: none;
    }

    .withdrawal-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        max-width: 1200px;
    }

    /* Withdrawal Header */
    .withdrawal-header {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .withdrawal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .withdrawal-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .withdrawal-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .withdrawal-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
    }

    .wallet-balance {
        background: rgba(240, 185, 11, 0.1);
        border-radius: 8px;
        padding: 15px 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        border: 1px solid rgba(240, 185, 11, 0.2);
    }

    .balance-value {
        font-size: 24px;
        font-weight: 600;
        color: #f0b90b;
        display: flex;
        align-items: center;
    }

    .balance-value i {
        margin-right: 8px;
    }

    .balance-label {
        font-size: 14px;
        color: #848e9c;
        margin-top: 4px;
    }

    /* Withdrawal Card */
    .withdrawal-card {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        margin-bottom: 25px;
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .withdrawal-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .card-body {
        padding: 30px;
    }

    /* Form Styling */
    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        display: block;
        margin-bottom: 10px;
        font-size: 14px;
        color: #848e9c;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 12px 15px;
        color: #eaecef;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: rgba(240, 185, 11, 0.5);
        box-shadow: 0 0 0 2px rgba(240, 185, 11, 0.1);
    }

    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-success {
        background: linear-gradient(135deg, #0ecb81 0%, #0ca368 100%);
        color: #fff;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #0ca368 0%, #0ecb81 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(14, 203, 129, 0.3);
    }

    .card-footer {
        padding: 20px 30px;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        justify-content: flex-end;
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>

<div class="withdrawal-wrapper">
    <!-- Withdrawal Header with Balance -->
    <div class="withdrawal-header">
        <div class="withdrawal-title">
            <h2><i class="fas fa-money-bill-wave"></i> Withdraw <?php echo $type2; ?></h2>
            <div class="wallet-balance">
                <div class="balance-value">
                    <i class="fas fa-wallet"></i>
                    <span class="count-number"><?php echo $user->$wallet_field*1;?></span>
                    <span class="currency"> <?php echo SITE_CURRENCY;?></span>
                </div>
                <div class="balance-label"><?php echo ($type == 11) ? 'Working Wallet Balance' : 'Wallet Balance';?></div>
            </div>
        </div>
    </div>

    <!-- Withdrawal Card -->
    <div class="withdrawal-card">
        <div class="card-body">
            <h3 style="color: #f0b90b; margin-bottom: 25px; text-align: center;"><i class="fas fa-hand-holding-usd"></i> Request Withdrawal</h3>

            <?php
            $current_day = date('l');
            if ($current_day != 'Sunday') { ?>
                <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 15px; margin-bottom: 20px; text-align: center;">
                    <i class="fas fa-calendar-alt" style="color: #ffc107; margin-right: 8px;"></i>
                    <strong style="color: #ffc107;">Notice:</strong>
                    <span style="color: #eaecef;">Withdrawal requests are only accepted on <strong>Sundays</strong>. Today is <strong><?php echo $current_day; ?></strong>.</span>
                </div>
            <?php } else { ?>
                <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: 8px; padding: 15px; margin-bottom: 20px; text-align: center;">
                    <i class="fas fa-check-circle" style="color: #28a745; margin-right: 8px;"></i>
                    <strong style="color: #28a745;">Available:</strong>
                    <span style="color: #eaecef;">You can submit withdrawal requests today (Sunday).</span>
                </div>
            <?php } ?>

            <form action="withdrawal_block_model.php" method="post" onSubmit="return abc_();">
                <div class="form-group">
                    <label for="amount" class="form-label"><?php echo $type2;?> Amount *</label>
                    <input class="form-control" type="text" id="amount" name="amount" value="" maxlength="10" required="required" placeholder="Enter amount to withdraw" <?php echo ($current_day != 'Sunday') ? 'disabled' : ''; ?>>
                    <small style="color: #848e9c; margin-top: 8px; display: block;">Available balance: <?php echo $user->$wallet_field*1;?> <?php echo SITE_CURRENCY;?></small>
                </div>

                <div class="card-footer">
                    <input type="hidden" name="type" value="<?php echo $type;?>" />
                    <button type="submit" class="btn btn-success" <?php echo ($current_day != 'Sunday') ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''; ?>>
                        <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>
                        <?php echo ($current_day != 'Sunday') ? 'Withdrawal Not Available' : 'Submit Withdrawal'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php include_once 'footer.php'; ?>
<?php if(SITE_CURRENCY_ == 'TRX'){?>
<script src="https://cdn.jsdelivr.net/npm/tronweb@2.4.1/dist/TronWeb.node.min.js"></script>
<?php /*<script src="../contract/tron/TronWeb.js"></script>*/?>
<script src="../contract/tron/index.js"></script>
<script src="../contract/tron/login.js"></script>
<?php }elseif(SITE_CURRENCY_ == 'USDT'){?>
<script src="https://cdn.jsdelivr.net/gh/ethereum/web3.js@1.0.0-beta.34/dist/web3.min.js"></script>
<script type="text/javascript" src="../contract/USDT/index.js"></script>
<script type="text/javascript" src="../contract/USDT/login.js"></script>
<script type="text/javascript" src="../contract/USDT/script.js"></script>
<?php }else{?>
<script src="https://cdn.jsdelivr.net/gh/ethereum/web3.js@1.0.0-beta.34/dist/web3.min.js"></script>
<script src="../contract/eth/index.js"></script>
<script src="../contract/eth/login.js"></script>
<?php }?>
<script>
    function abc_(){
        if (typeof web3 !== 'undefined') {
            await ethereum.enable();
            var waddress = await getAccounts();
            if (typeof waddress !== 'undefined' && waddress == '<?php echo $user->USDT_address;?>'){

            }
            else{
                alert('Authorization error');
                return false;
            }
        }
        else{
            alert('Authorization error');
            return false;
        }
    }
</script>