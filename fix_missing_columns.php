<?php
// Fix missing columns in database tables
include_once '.env.php';

echo "<h2>🔧 Fixing Missing Database Columns</h2>";

$connection = mysqli_connect($_ENV['DB_HOST'], $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD'], $_ENV['DB_NAME']);

if (!$connection) {
    die("<p style='color: red;'>❌ Connection failed: " . mysqli_connect_error() . "</p>");
}

echo "<p style='color: green;'>✅ Connected to database successfully</p>";

// Array of ALTER TABLE statements to add missing columns
$alterations = [
    'user_type' => "ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `type` varchar(20) DEFAULT 'member' AFTER `uid`",
    'user_login_detail' => "CREATE TABLE IF NOT EXISTS `user_login_detail` (
        `recid` int(11) NOT NULL AUTO_INCREMENT,
        `uid` int(11) NOT NULL,
        `datetime` datetime NOT NULL,
        `ip` varchar(45) DEFAULT NULL,
        PRIMARY KEY (`recid`)
    )",
    'admin_status' => "ALTER TABLE `admin` ADD COLUMN IF NOT EXISTS `status` tinyint(1) DEFAULT 0 AFTER `recid`",
    'admin_login' => "ALTER TABLE `admin` ADD COLUMN IF NOT EXISTS `login_id` varchar(100) DEFAULT 'admin' AFTER `status`",
    'admin_password' => "ALTER TABLE `admin` ADD COLUMN IF NOT EXISTS `password` varchar(255) DEFAULT '' AFTER `login_id`"
];

$success_count = 0;
$total_alterations = count($alterations);

foreach ($alterations as $alteration_name => $sql) {
    echo "<h3>Applying: <code>$alteration_name</code></h3>";
    
    if (mysqli_query($connection, $sql)) {
        echo "<p style='color: green;'>✅ '$alteration_name' applied successfully</p>";
        $success_count++;
    } else {
        $error = mysqli_error($connection);
        if (strpos($error, 'Duplicate column') !== false || strpos($error, 'already exists') !== false) {
            echo "<p style='color: blue;'>ℹ '$alteration_name' already exists, skipping</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>❌ Error applying '$alteration_name': $error</p>";
        }
    }
}

// Insert default admin user if not exists
echo "<h3>Setting up default admin user...</h3>";

// Check if admin user exists
$check_admin = mysqli_query($connection, "SELECT recid FROM admin WHERE recid = 1");
if (mysqli_num_rows($check_admin) == 0) {
    // Insert default admin
    $default_password = encryptPassword('admin123'); // You can change this
    $insert_admin = "INSERT INTO `admin` (`recid`, `status`, `login_id`, `password`) VALUES (1, 0, 'admin', '$default_password')";
    
    if (mysqli_query($connection, $insert_admin)) {
        echo "<p style='color: green;'>✅ Default admin user created</p>";
        echo "<p><strong>Admin Login:</strong> admin / admin123</p>";
    } else {
        echo "<p style='color: red;'>❌ Error creating admin user: " . mysqli_error($connection) . "</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ Admin user already exists</p>";
}

// Function to encrypt password (same as in function_lib.php)
function encryptPassword($plainPassword) {
    $salt = 'lkjfghdsa';
    $pepper = 'lkjfghdsa';    
    return sha1($salt . sha1($plainPassword . $pepper));
}

mysqli_close($connection);

echo "<hr>";
echo "<h3>📊 Summary</h3>";
echo "<p><strong>Alterations applied:</strong> $success_count / $total_alterations</p>";

if ($success_count == $total_alterations) {
    echo "<p style='color: green; font-size: 18px;'>🎉 <strong>All fixes applied successfully!</strong></p>";
    echo "<p>The 'type' column and other missing columns have been added to your database.</p>";
    echo "<p>Your application should now work without the column errors.</p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>Test Main Application</a></li>";
    echo "<li><a href='test_connection.php' target='_blank'>Test Connection</a></li>";
    echo "<li><a href='admin/' target='_blank'>Admin Panel</a> (admin / admin123)</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Some fixes failed to apply. Please check the errors above.</p>";
}

echo "<p><em>Database fixes completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
