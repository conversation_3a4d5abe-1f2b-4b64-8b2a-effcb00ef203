<?php
// Create essential database tables
include_once '.env.php';

echo "<h2>🗄️ Creating Essential Database Tables</h2>";

$connection = mysqli_connect($_ENV['DB_HOST'], $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD'], $_ENV['DB_NAME']);

if (!$connection) {
    die("<p style='color: red;'>❌ Connection failed: " . mysqli_connect_error() . "</p>");
}

echo "<p style='color: green;'>✅ Connected to database successfully</p>";

// Array of SQL statements to create tables
$tables = [
    'admin' => "CREATE TABLE IF NOT EXISTS `admin` (
        `recid` int(11) NOT NULL AUTO_INCREMENT,
        `site_name` varchar(100) DEFAULT 'HelloTrade',
        `site_slogan` varchar(200) DEFAULT 'Trading Platform',
        `site_url` varchar(100) DEFAULT 'http://localhost',
        `email` varchar(100) DEFAULT '<EMAIL>',
        `email_info` varchar(100) DEFAULT '<EMAIL>',
        `email_mail` varchar(100) DEFAULT '<EMAIL>',
        `email_support` varchar(100) DEFAULT '<EMAIL>',
        `email_sales` varchar(100) DEFAULT '<EMAIL>',
        `phone` varchar(20) DEFAULT '+1234567890',
        `mobile` varchar(20) DEFAULT '+1234567890',
        `address` text DEFAULT 'Your Address',
        `city` varchar(50) DEFAULT 'Your City',
        `state` varchar(50) DEFAULT 'Your State',
        `pincode` varchar(10) DEFAULT '123456',
        `tds` decimal(5,2) DEFAULT 0.00,
        `service_tax` decimal(5,2) DEFAULT 0.00,
        `service` decimal(5,2) DEFAULT 0.00,
        `working_status` tinyint(1) DEFAULT 1,
        `sms_count` int(11) DEFAULT 0,
        `visitors` int(11) DEFAULT 0,
        `bv_value` decimal(10,2) DEFAULT 1.00,
        `capping_binary` decimal(10,2) DEFAULT 1000.00,
        `capping_gift` decimal(10,2) DEFAULT 500.00,
        `logo` varchar(200) DEFAULT 'logo.png',
        `user_sms_format` text DEFAULT 'Hello {name}, your message here.',
        `otp` tinyint(1) DEFAULT 1,
        `coin_rate` decimal(10,4) DEFAULT 1.0000,
        `b_rate` decimal(10,4) DEFAULT 1.0000,
        `s_rate` decimal(10,4) DEFAULT 1.0000,
        PRIMARY KEY (`recid`)
    )",
    
    'user' => "CREATE TABLE IF NOT EXISTS `user` (
        `uid` int(11) NOT NULL AUTO_INCREMENT,
        `login_id` varchar(100) NOT NULL,
        `name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `mobile` varchar(20) DEFAULT NULL,
        `password` varchar(255) NOT NULL,
        `sponsor` varchar(100) DEFAULT NULL,
        `position` varchar(10) DEFAULT NULL,
        `wallet_topup` decimal(10,2) DEFAULT 0.00,
        `wallet_income` decimal(10,2) DEFAULT 0.00,
        `status` tinyint(1) DEFAULT 0,
        `datetime` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`uid`),
        UNIQUE KEY `login_id` (`login_id`),
        UNIQUE KEY `email` (`email`)
    )",
    
    'message' => "CREATE TABLE IF NOT EXISTS `message` (
        `recid` int(11) NOT NULL AUTO_INCREMENT,
        `sender` int(11) NOT NULL,
        `receiver` int(11) NOT NULL,
        `subject` varchar(200) NOT NULL,
        `message` text NOT NULL,
        `filename` varchar(200) DEFAULT NULL,
        `read` tinyint(1) DEFAULT 0,
        `datetime` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`recid`)
    )",
    
    'deposit_block' => "CREATE TABLE IF NOT EXISTS `deposit_block` (
        `recid` int(11) NOT NULL AUTO_INCREMENT,
        `uid` int(11) NOT NULL,
        `txid` varchar(200) NOT NULL,
        `address` varchar(200) NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `amount_coin` decimal(10,8) NOT NULL,
        `status` tinyint(1) DEFAULT 0,
        `datetime` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`recid`)
    )",
    
    'settings' => "CREATE TABLE IF NOT EXISTS `settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text,
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    )"
];

$success_count = 0;
$total_tables = count($tables);

foreach ($tables as $table_name => $sql) {
    echo "<h3>Creating table: <code>$table_name</code></h3>";
    
    if (mysqli_query($connection, $sql)) {
        echo "<p style='color: green;'>✅ Table '$table_name' created successfully</p>";
        $success_count++;
    } else {
        echo "<p style='color: red;'>❌ Error creating table '$table_name': " . mysqli_error($connection) . "</p>";
    }
}

// Insert default admin record
echo "<h3>Inserting default data...</h3>";

$insert_admin = "INSERT IGNORE INTO `admin` (`recid`) VALUES (1)";
if (mysqli_query($connection, $insert_admin)) {
    echo "<p style='color: green;'>✅ Default admin record created</p>";
} else {
    echo "<p style='color: orange;'>⚠ Admin record may already exist: " . mysqli_error($connection) . "</p>";
}

mysqli_close($connection);

echo "<hr>";
echo "<h3>📊 Summary</h3>";
echo "<p><strong>Tables created:</strong> $success_count / $total_tables</p>";

if ($success_count == $total_tables) {
    echo "<p style='color: green; font-size: 18px;'>🎉 <strong>All tables created successfully!</strong></p>";
    echo "<p>Your application should now work. Try accessing it:</p>";
    echo "<ul>";
    echo "<li><a href='index.php' target='_blank'>Main Application</a></li>";
    echo "<li><a href='test_connection.php' target='_blank'>Test Connection</a></li>";
    echo "<li><a href='http://localhost/phpmyadmin/' target='_blank'>phpMyAdmin</a></li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Some tables failed to create. Please check the errors above.</p>";
}

echo "<p><em>Table creation completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
